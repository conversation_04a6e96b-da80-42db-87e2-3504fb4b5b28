package se.skatteverket;

import io.quarkiverse.cxf.annotation.CXFClient;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.net.ssl.*;
import java.io.*;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.X509Certificate;

/**
 * Test class demonstrating how to call the Skatteverket PersonpostXML SOAP service with mutual TLS
 *
 * Prerequisites:
 * 1. Run generate-mtls-certificates.bat to create client certificates
 * 2. Copy kommun-a.p12 to src/test/resources/ (keystore for Skatteverket service)
 * 3. Start the Quarkus application with mutual TLS enabled
 * 4. Run ./mvnw clean generate-sources to generate PersonpostXMLInterface client from WSDL
 *
 * This test uses Quarkus CXF client configuration from application.properties
 * for automatic mutual TLS setup with the PersonpostXMLInterface.
 */
@QuarkusTest
public class SoapClientTest2 {

    // Inject the SOAP client configured via application.properties (using generated client)
    @CXFClient("personpostXMLClient")
    se.skatteverket.client.PersonpostXMLInterface personpostXMLInterface;

    // Inject the service URL from application.properties
    @ConfigProperty(name = "skatteverket.service.url")
    String serviceUrl;

/*
    @BeforeEach
    void setupSSL() {
        // Set system properties to disable SSL verification for testing
        System.setProperty("javax.net.ssl.trustStore", "src/main/resources/truststore.p12");
        System.setProperty("javax.net.ssl.trustStorePassword", "changeit");
        System.setProperty("javax.net.ssl.trustStoreType", "PKCS12");

        // Disable hostname verification for localhost
        System.setProperty("com.sun.net.ssl.checkRevocation", "false");
        System.setProperty("javax.net.ssl.trustStoreProvider", "SUN");

        // Alternative: disable all SSL verification (for testing only!)
        System.setProperty("javax.net.ssl.trustStore", "");
        System.setProperty("javax.net.ssl.trustStorePassword", "");

        System.out.println("SSL system properties configured for testing");
    } */



    @Test
    //@Disabled("Enable this test when you want to test getData with PersonpostXML client")
    public void testGetDataWithQuarkusClient() throws Exception {
        System.out.println("Calling getData on: " + serviceUrl);

        // Create a test PersonpostRequest object
        se.skatteverket.client.PersonpostRequestTYPE request = createTestPersonpostRequest();

        try {
            // Call the service using the injected client (mutual TLS configured automatically)
            se.skatteverket.client.ResponseXMLTYPE response = personpostXMLInterface.getData(request);


            System.out.println("PersonpostXML getData Response received successfully!");
            System.out.println("========================================");

            // Print basic response information
            System.out.println("Response object: " + response.getClass().getName());
            System.out.println("Folkbokforingsposter: " + response.getFolkbokforingsposter());

            // Print detailed response content
            System.out.println("\n--- Response Details ---");
            if (response.getFolkbokforingsposter() != null) {
                var folkbokforingsposter = response.getFolkbokforingsposter();
                System.out.println("Folkbokforingsposter class: " + folkbokforingsposter.getClass().getName());

                // Try to access the folkbokforingspost list
                try {
                    var posts = folkbokforingsposter.getFolkbokforingspost();
                    System.out.println("Number of folkbokforingspost entries: " + (posts != null ? posts.size() : "null"));

                    if (posts != null && !posts.isEmpty()) {
                        System.out.println("First entry details:");
                        var firstPost = posts.get(0);
                        System.out.println("  - Class: " + firstPost.getClass().getName());
                        System.out.println("  - Object: " + firstPost.toString());
                    }
                } catch (Exception e) {
                    System.out.println("Could not access folkbokforingspost list: " + e.getMessage());
                }
            } else {
                System.out.println("Folkbokforingsposter is null");
            }

            // Try JAXB marshalling as fallback
            try {
                System.out.println("\n--- XML Marshalling ---");
                JAXBContext jaxbContext = JAXBContext.newInstance(se.skatteverket.client.ResponseXMLTYPE.class);
                Marshaller marshaller = jaxbContext.createMarshaller();
                marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

                StringWriter sw = new StringWriter();
                marshaller.marshal(response, sw);
                System.out.println(sw.toString());
            } catch (Exception xmlException) {
                System.out.println("JAXB marshalling failed: " + xmlException.getMessage());
                System.out.println("Raw response toString(): " + response.toString());
            }

            System.out.println("========================================");


            // Assertions
            assert response != null;
            assert response.getFolkbokforingsposter() != null;

        } catch (se.skatteverket.client.NaWebServiceException e) {
            System.out.println("========================================");
            System.out.println("NaWebServiceException caught:");
            System.out.println("Message: " + e.getMessage());
            System.out.println("Cause: " + e.getCause());
            if (e.getFaultInfo() != null) {
                System.out.println("Fault Info: " + e.getFaultInfo());
            }
            System.out.println("========================================");
            // This is expected for test data, just verify the client works
            assert e != null;
        } catch (Exception e) {
            System.out.println("========================================");
            System.out.println("Unexpected exception:");
            System.out.println("Type: " + e.getClass().getName());
            System.out.println("Message: " + e.getMessage());
            e.printStackTrace();
            System.out.println("========================================");
            throw e; // Re-throw unexpected exceptions
        }

        // Verify client is properly configured
        assert personpostXMLInterface != null;
    }

    /**
     * Create a test PersonpostRequest for testing purposes
     * This creates a minimal valid request structure
     */
    private se.skatteverket.client.PersonpostRequestTYPE createTestPersonpostRequest() {
        se.skatteverket.client.PersonpostRequestTYPE request = new se.skatteverket.client.PersonpostRequestTYPE();

        // Create Bestallning (order information)
        se.skatteverket.client.BestallningTYPE bestallning = new se.skatteverket.client.BestallningTYPE();
        bestallning.setOrgNr(162021004748L); // Test organization number
        bestallning.setBestallningsId("00000236-FO01-0002"); // Test order ID

        request.setBestallning(bestallning);
        request.setPersonId(195811112217L); // Test person ID (Swedish personnummer format)

        return request;
    }
}
