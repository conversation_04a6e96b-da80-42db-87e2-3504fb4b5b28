# Test Configuration for Mutual TLS SOAP Client
# This configuration is used when running tests

# Service endpoint configuration (pointing to external running server)
soap.service.url=https://localhost:8444/soap/HelloWorldService
skatteverket.service.url=https://www2.test.skatteverket.se/na/na_epersondata/V4/personpostXML
#skatteverket.service.url=https://localhost:8445/soap/PersonpostXMLService

# CXF Client Configuration for SOAP calls using generated client
quarkus.cxf.client.helloWorldClient.client-endpoint-url=${soap.service.url}
quarkus.cxf.client.helloWorldClient.service-interface=org.example.client.HelloWorldService

# CXF Client Configuration for Skatteverket PersonpostXML service
quarkus.cxf.client.personpostXMLClient.client-endpoint-url=${skatteverket.service.url}
quarkus.cxf.client.personpostXMLClient.service-interface=se.skatteverket.client.PersonpostXMLInterface

# Use named TLS configuration for the HelloWorld client (this is the supported approach)
quarkus.cxf.client.helloWorldClient.tls-configuration-name=helloworld-client-tls

# Use named TLS configuration for the Skatteverket client with Kommun-a.p12
quarkus.cxf.client.personpostXMLClient.tls-configuration-name=skatteverket-client-tls

# Named TLS configuration for the HelloWorld client with mutual TLS
quarkus.tls.helloworld-client-tls.key-store.p12.path=client-keystore.p12
quarkus.tls.helloworld-client-tls.key-store.p12.password=changeit

# Named TLS configuration for the Skatteverket client with Kommun-a.p12
quarkus.tls.skatteverket-client-tls.key-store.p12.path=kommun-a.p12
quarkus.tls.skatteverket-client-tls.key-store.p12.password=5484220231081583

# Trust all certificates for testing (disable certificate validation)
quarkus.tls.helloworld-client-tls.trust-all=true
quarkus.tls.skatteverket-client-tls.trust-all=true

# Disable hostname verification for localhost testing (NONE = disable)
quarkus.tls.helloworld-client-tls.hostname-verification-algorithm=NONE
quarkus.tls.skatteverket-client-tls.hostname-verification-algorithm=NONE

# Logging for debugging
quarkus.log.category."org.apache.cxf".level=DEBUG
quarkus.log.category."io.quarkiverse.cxf".level=DEBUG
