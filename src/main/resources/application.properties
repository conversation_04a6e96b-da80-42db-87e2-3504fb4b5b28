# Application Configuration
quarkus.application.name=quarkus-soap-service

# HTTP Configuration
quarkus.http.port=8082
quarkus.http.ssl-port=8444

# HTTPS/SSL Configuration with Mutual TLS
quarkus.http.ssl.certificate.key-store-file=keystore.p12
quarkus.http.ssl.certificate.key-store-password=changeit
quarkus.http.ssl.certificate.key-store-file-type=PKCS12

# Mutual TLS Configuration - Client Certificate Authentication
quarkus.http.ssl.certificate.trust-store-file=truststore.p12
quarkus.http.ssl.certificate.trust-store-password=changeit
quarkus.http.ssl.certificate.trust-store-file-type=PKCS12
quarkus.http.ssl.client-auth=required

# Redirect HTTP to HTTPS
quarkus.http.insecure-requests=redirect

# CXF Configuration
quarkus.cxf.path=/soap

# REST Client Configuration for Hello World service
rest.service.url=https://localhost:8444/api/hello
quarkus.rest-client.hello-world-rest-client.url=https://localhost:8444
quarkus.rest-client.hello-world-rest-client.tls-configuration-name=helloworld-rest-client-tls

# Named TLS configuration for the REST client with mutual TLS
quarkus.tls.helloworld-rest-client-tls.key-store.p12.path=client-keystore.p12
quarkus.tls.helloworld-rest-client-tls.key-store.p12.password=changeit
quarkus.tls.helloworld-rest-client-tls.trust-all=true
quarkus.tls.helloworld-rest-client-tls.hostname-verification-algorithm=NONE

# Logging Configuration
quarkus.log.console.enable=true
quarkus.log.console.level=INFO
quarkus.log.category."org.apache.cxf".level=DEBUG
quarkus.log.category."io.quarkiverse.cxf".level=DEBUG

# Development mode configuration
%dev.quarkus.http.ssl.certificate.key-store-file=src/main/resources/keystore.p12
%dev.quarkus.http.ssl.certificate.trust-store-file=src/main/resources/truststore.p12
%dev.quarkus.log.console.level=DEBUG
