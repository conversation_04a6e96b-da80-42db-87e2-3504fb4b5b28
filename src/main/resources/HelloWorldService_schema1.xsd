<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://example.org/" xmlns:tns="http://example.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:element name="sayHello" type="tns:sayHello"/>

  <xs:element name="sayHelloResponse" type="tns:sayHelloResponse"/>

  <xs:element name="getServerTime" type="tns:getServerTime"/>

  <xs:element name="getServerTimeResponse" type="tns:getServerTimeResponse"/>

  <xs:element name="echo" type="tns:echo"/>

  <xs:element name="echoResponse" type="tns:echoResponse"/>

  <xs:complexType name="sayHello">
    <xs:sequence>
      <xs:element name="name" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="sayHelloResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getServerTime">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="getServerTimeResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="echo">
    <xs:sequence>
      <xs:element name="message" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="echoResponse">
    <xs:sequence>
      <xs:element name="return" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
