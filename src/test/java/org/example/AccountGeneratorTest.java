package org.example;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

public class AccountGeneratorTest {

    private static class Account {
        public List<String> list1 = Collections.emptyList();
        public List<String> list2 = Collections.emptyList();
    }

    @Test
    void testGenerateAccountNumber() throws IOException {
        List<Account> object = IntStream.range(1, 11).mapToObj(this::createWithIndex)
                .toList();

        System.out.println(new ObjectMapper().writeValueAsString(object));
        Assertions.assertTrue(true);
    }

    private Account createWithIndex(int i) {
        Account account = new Account();


        return account;
    }


    private static String getLuhnCheckDigit(String number) {
        int sum = 0;
        boolean doubleDigit = true;
        for (int i = number.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(number.charAt(i));
            if (doubleDigit) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            sum += digit;
            doubleDigit = !doubleDigit;
        }
        return String.valueOf((10 - (sum % 10)) % 10);
    }
}
