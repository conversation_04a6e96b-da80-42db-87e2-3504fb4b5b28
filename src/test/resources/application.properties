# Test Configuration for Mutual TLS SOAP Client
# This configuration is used when running tests

# Service endpoint configuration (pointing to external running server)
soap.service.url=https://localhost:8444/soap/HelloWorldService
rest.service.url=https://localhost:8444/api/hello

# CXF Client Configuration for SOAP calls using generated client
quarkus.cxf.client.helloWorldClient.client-endpoint-url=${soap.service.url}
quarkus.cxf.client.helloWorldClient.service-interface=org.example.client.HelloWorldService

# REST Client Configuration for Hello World service
quarkus.rest-client.hello-world-rest-client.url=https://localhost:8444
quarkus.rest-client.hello-world-rest-client.tls-configuration-name=helloworld-rest-client-tls

# Use named TLS configuration for the HelloWorld client (this is the supported approach)
quarkus.cxf.client.helloWorldClient.tls-configuration-name=helloworld-client-tls

# Named TLS configuration for the HelloWorld client with mutual TLS
quarkus.tls.helloworld-client-tls.key-store.p12.path=client-keystore.p12
quarkus.tls.helloworld-client-tls.key-store.p12.password=changeit

# Named TLS configuration for the REST client with mutual TLS
quarkus.tls.helloworld-rest-client-tls.key-store.p12.path=client-keystore.p12
quarkus.tls.helloworld-rest-client-tls.key-store.p12.password=changeit

# Trust all certificates for testing (disable certificate validation)
quarkus.tls.helloworld-client-tls.trust-all=true
quarkus.tls.helloworld-rest-client-tls.trust-all=true

# Disable hostname verification for localhost testing (NONE = disable)
quarkus.tls.helloworld-client-tls.hostname-verification-algorithm=NONE
quarkus.tls.helloworld-rest-client-tls.hostname-verification-algorithm=NONE

# Logging for debugging
quarkus.log.category."org.apache.cxf".level=DEBUG
quarkus.log.category."io.quarkiverse.cxf".level=DEBUG
